import React, { useState } from 'react';
import {
  ObjectFieldTemplateProps,
} from '@rjsf/utils';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

const CustomObjectFieldTemplate: React.FC<ObjectFieldTemplateProps> = ({
  idSchema,
  properties,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  if (!properties.length) return null;

  console.log(properties, 'properties');
  console.log(idSchema, 'idSchema');
  
  return (
    <>
      <span id={idSchema.$id} />
      {!isExpanded && (
        <ExpandMore
          fontSize='small'
          color='action'
          onClick={() => setIsExpanded(prev => !prev)}
        />
      )}
      {isExpanded && (
        <ExpandLess
          fontSize='small'
          color='action'
          onClick={() => setIsExpanded(prev => !prev)}
        />
      )}
      {isExpanded && (
        <div className='card-shadow'>
          {properties.map(items => (
            <div key={items.name} className='property-wrapper field-content'>
              {items.content}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

export {
  CustomObjectFieldTemplate,
};